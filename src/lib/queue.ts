import { Queue } from "bullmq"
import { redis } from "./redis"

export const proxyQueue = new Queue("proxy-transcode", { connection: redis })
export const variantQueue = new Queue("variant-render", { connection: redis })
export const complianceQueue = new Queue("compliance-scan", { connection: redis })

export interface ProxyTranscodeJob {
  cutId: string
  sourceAssetId: string
}

export interface VariantRenderJob {
  variantId: string
  cutId: string
  changeListId: string
}

export interface ComplianceScanJob {
  cutId: string
  sourceAssetId: string
}
