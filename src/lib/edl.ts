export interface EDLClip {
  src: string
  inMs: number
  outMs: number
  audioGain?: number
}

export interface EDL {
  clips: EDLClip[]
  metadata?: {
    title?: string
    description?: string
    version?: string
  }
}

export function validateEDL(edl: any): EDL {
  if (!edl || !Array.isArray(edl.clips)) {
    throw new Error("Invalid EDL format")
  }

  return {
    clips: edl.clips.map((clip: any) => ({
      src: clip.src,
      inMs: clip.inMs,
      outMs: clip.outMs,
      audioGain: clip.audioGain || 1.0,
    })),
    metadata: edl.metadata || {},
  }
}

export function generatePremiereXML(edl: EDL): string {
  const clips = edl.clips
    .map(
      (clip, index) => `
    <clipitem id="clipitem-${index}">
      <name>${clip.src}</name>
      <in>${Math.floor((clip.inMs / 1000) * 30)}</in>
      <out>${Math.floor((clip.outMs / 1000) * 30)}</out>
      <file id="file-${index}">
        <name>${clip.src}</name>
        <pathurl>file://localhost/${clip.src}</pathurl>
      </file>
    </clipitem>`,
    )
    .join("")

  return `<?xml version="1.0" encoding="UTF-8"?>
<xmeml version="4">
  <project>
    <name>${edl.metadata?.title || "CrowdScreen Export"}</name>
    <children>
      <sequence>
        <name>Main Sequence</name>
        <media>
          <video>
            <track>${clips}</track>
          </video>
        </media>
      </sequence>
    </children>
  </project>
</xmeml>`
}
