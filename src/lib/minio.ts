import { Client } from "minio"
import { env } from "./env"

export const minioClient = new Client({
  endPoint: env.MINIO_ENDPOINT,
  port: Number.parseInt(env.MINIO_PORT),
  useSSL: false,
  accessKey: env.MINIO_ACCESS_KEY,
  secretKey: env.MINIO_SECRET_KEY,
})

export async function ensureBucket() {
  const exists = await minioClient.bucketExists(env.MINIO_BUCKET)
  if (!exists) {
    await minioClient.makeBucket(env.MINIO_BUCKET)
  }
}

export function getSignedUrl(key: string, expires = 3600) {
  return minioClient.presignedGetObject(env.MINIO_BUCKET, key, expires)
}

export function getUploadUrl(key: string, expires = 3600) {
  return minioClient.presignedPutObject(env.MINIO_BUCKET, key, expires)
}
