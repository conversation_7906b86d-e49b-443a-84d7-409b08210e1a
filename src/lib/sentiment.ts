export interface ReactionCounts {
  love: number
  like: number
  laugh: number
  gasp: number
  confused: number
  sad: number
}

export function calculateSentimentScore(reactions: ReactionCounts): number {
  const positive = reactions.love * 2 + reactions.like + reactions.laugh * 1.5 + reactions.gasp * 1.2
  const negative = reactions.confused + reactions.sad * 1.3
  return positive - negative
}

export function smoothSentiment(scores: number[], windowSize = 5): number[] {
  return scores.map((_, index) => {
    const start = Math.max(0, index - Math.floor(windowSize / 2))
    const end = Math.min(scores.length, start + windowSize)
    const window = scores.slice(start, end)
    return window.reduce((sum, score) => sum + score, 0) / window.length
  })
}

export function detectTroughs(
  scores: number[],
  threshold = 0.25,
  minDuration = 5,
): Array<{ start: number; end: number; severity: number }> {
  const median = [...scores].sort((a, b) => a - b)[Math.floor(scores.length / 2)]
  const troughThreshold = median * (1 - threshold)

  const troughs: Array<{ start: number; end: number; severity: number }> = []
  let currentTrough: { start: number; end: number; severity: number } | null = null

  scores.forEach((score, index) => {
    if (score < troughThreshold) {
      if (!currentTrough) {
        currentTrough = { start: index, end: index, severity: median - score }
      } else {
        currentTrough.end = index
        currentTrough.severity = Math.max(currentTrough.severity, median - score)
      }
    } else if (currentTrough && currentTrough.end - currentTrough.start >= minDuration) {
      troughs.push(currentTrough)
      currentTrough = null
    } else {
      currentTrough = null
    }
  })

  return troughs
}
