export interface ComplianceIssue {
  type: "flashing" | "profanity" | "loudness"
  severity: "low" | "medium" | "high"
  startMs: number
  endMs: number
  description: string
  recommendation: string
}

export interface ComplianceResult {
  score: number // 0-100
  issues: ComplianceIssue[]
  summary: {
    flashingRisk: number
    profanityCount: number
    loudnessViolations: number
  }
}

export function calculateComplianceScore(issues: ComplianceIssue[]): number {
  let score = 100

  issues.forEach((issue) => {
    const penalty = issue.severity === "high" ? 20 : issue.severity === "medium" ? 10 : 5
    score -= penalty
  })

  return Math.max(0, score)
}

export function generateComplianceReport(result: ComplianceResult): string {
  const { score, issues, summary } = result

  let report = `# Compliance Report\n\n`
  report += `**Overall Score:** ${score}/100\n\n`

  if (summary.flashingRisk > 0) {
    report += `**Flashing/Strobe Risk:** ${summary.flashingRisk} instances detected\n`
  }

  if (summary.profanityCount > 0) {
    report += `**Profanity Issues:** ${summary.profanityCount} instances detected\n`
  }

  if (summary.loudnessViolations > 0) {
    report += `**Loudness Violations:** ${summary.loudnessViolations} instances detected\n`
  }

  report += `\n## Issues\n\n`

  issues.forEach((issue, index) => {
    report += `### Issue ${index + 1}: ${issue.type.toUpperCase()}\n`
    report += `- **Severity:** ${issue.severity}\n`
    report += `- **Time:** ${formatTime(issue.startMs)} - ${formatTime(issue.endMs)}\n`
    report += `- **Description:** ${issue.description}\n`
    report += `- **Recommendation:** ${issue.recommendation}\n\n`
  })

  return report
}

function formatTime(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`
}
