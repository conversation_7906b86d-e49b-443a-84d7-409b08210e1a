import { Worker } from "bullmq"
import { redis } from "@/lib/redis"
import { db } from "@/lib/db"
import { env } from "@/lib/env"
import ffmpeg from "fluent-ffmpeg"
import type { ProxyTranscodeJob, VariantRenderJob, ComplianceScanJob } from "@/lib/queue"
import { type ComplianceIssue, calculateComplianceScore } from "@/lib/compliance"

// Set ffmpeg paths
if (env.FFMPEG_PATH) {
  ffmpeg.setFfmpegPath(env.FFMPEG_PATH)
}
if (env.FFPROBE_PATH) {
  ffmpeg.setFfprobePath(env.FFPROBE_PATH)
}

// Proxy Transcode Worker
const proxyWorker = new Worker<ProxyTranscodeJob>(
  "proxy-transcode",
  async (job) => {
    const { cutId, sourceAssetId } = job.data

    console.log(`Starting proxy transcode for cut ${cutId}`)

    try {
      // Update job status
      await db.renderJob.create({
        data: {
          projectId: (await db.cut.findUnique({ where: { id: cutId }, select: { projectId: true } }))!.projectId,
          type: "proxy",
          status: "PROCESSING",
          metadata: { cutId, sourceAssetId },
        },
      })

      const asset = await db.asset.findUnique({ where: { id: sourceAssetId } })
      if (!asset) throw new Error("Asset not found")

      // Generate HLS proxy
      const outputKey = `hls/${asset.projectId}/${cutId}/index.m3u8`
      const tempDir = `/tmp/${cutId}`

      await new Promise((resolve, reject) => {
        ffmpeg(asset.originalUrl)
          .outputOptions([
            "-c:v libx264",
            "-c:a aac",
            "-f hls",
            "-hls_time 10",
            "-hls_list_size 0",
            "-hls_segment_filename",
            `${tempDir}/segment_%03d.ts`,
          ])
          .output(`${tempDir}/index.m3u8`)
          .on("end", resolve)
          .on("error", reject)
          .run()
      })

      // Upload HLS files to MinIO
      // Implementation would upload all segments and playlist

      // Update cut with proxy URL
      await db.cut.update({
        where: { id: cutId },
        data: {
          proxyAssetId: sourceAssetId, // In real implementation, create new proxy asset
        },
      })

      console.log(`Completed proxy transcode for cut ${cutId}`)
    } catch (error) {
      console.error(`Proxy transcode failed for cut ${cutId}:`, error)
      throw error
    }
  },
  { connection: redis },
)

// Variant Render Worker
const variantWorker = new Worker<VariantRenderJob>(
  "variant-render",
  async (job) => {
    const { variantId, cutId, changeListId } = job.data

    console.log(`Starting variant render for variant ${variantId}`)

    try {
      const variant = await db.variant.findUnique({
        where: { id: variantId },
        include: {
          changeList: true,
          cut: {
            include: { sourceAsset: true },
          },
        },
      })

      if (!variant) throw new Error("Variant not found")

      await db.variant.update({
        where: { id: variantId },
        data: { status: "RENDERING" },
      })

      const edl = variant.changeList.edl as any
      const outputKey = `hls/${variant.cut.projectId}/${variantId}/index.m3u8`

      // Process EDL clips and render variant
      // Implementation would use ffmpeg concat demuxer

      console.log(`Completed variant render for variant ${variantId}`)

      await db.variant.update({
        where: { id: variantId },
        data: { status: "COMPLETED" },
      })
    } catch (error) {
      console.error(`Variant render failed for variant ${variantId}:`, error)

      await db.variant.update({
        where: { id: variantId },
        data: { status: "FAILED" },
      })

      throw error
    }
  },
  { connection: redis },
)

// Compliance Scan Worker
const complianceWorker = new Worker<ComplianceScanJob>(
  "compliance-scan",
  async (job) => {
    const { cutId, sourceAssetId } = job.data

    console.log(`Starting compliance scan for cut ${cutId}`)

    try {
      const cut = await db.cut.findUnique({
        where: { id: cutId },
        include: { sourceAsset: true, project: true },
      })

      if (!cut) throw new Error("Cut not found")

      const issues: ComplianceIssue[] = []

      // Flashing/Strobe Detection
      // Implementation would analyze frame luminance changes
      issues.push({
        type: "flashing",
        severity: "medium",
        startMs: 42000,
        endMs: 44000,
        description: "Rapid luminance changes detected",
        recommendation: "Apply luminance limiter or add content warning",
      })

      // Caption Profanity Check
      // Implementation would parse VTT/SRT files

      // Loudness Analysis
      // Implementation would use ffmpeg loudnorm filter

      const score = calculateComplianceScore(issues)

      const report = await db.complianceReport.create({
        data: {
          cutId,
          projectId: cut.projectId,
          score,
          issues: issues as any,
          reportUrl: `reports/${cut.projectId}/${cutId}/compliance.pdf`,
        },
      })

      console.log(`Completed compliance scan for cut ${cutId}, score: ${score}`)
    } catch (error) {
      console.error(`Compliance scan failed for cut ${cutId}:`, error)
      throw error
    }
  },
  { connection: redis },
)

console.log("Workers started successfully")

// Graceful shutdown
process.on("SIGINT", async () => {
  console.log("Shutting down workers...")
  await Promise.all([proxyWorker.close(), variantWorker.close(), complianceWorker.close()])
  process.exit(0)
})
