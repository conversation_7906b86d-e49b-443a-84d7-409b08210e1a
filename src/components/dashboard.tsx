"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Play, Upload, Shield, Users, TrendingUp, Clock } from "lucide-react"

interface Project {
  id: string
  name: string
  description?: string
  posterUrl?: string
  org: { name: string }
  _count: {
    cuts: number
    assets: number
  }
  updatedAt: string
}

interface DashboardProps {
  projects: Project[]
}

export function Dashboard({ projects }: DashboardProps) {
  const [activeJobs, setActiveJobs] = useState([
    { id: "1", type: "Transcoding", progress: 75, project: "Demo Project" },
    { id: "2", type: "Compliance Scan", progress: 45, project: "Short Film A" },
  ])

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <Play className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{projects.length}</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">3 live screenings</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">94%</div>
            <p className="text-xs text-muted-foreground">+5% from last scan</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">87%</div>
            <p className="text-xs text-muted-foreground">Average across all sessions</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="projects" className="space-y-4">
        <TabsList>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="jobs">Active Jobs</TabsTrigger>
          <TabsTrigger value="sessions">Live Sessions</TabsTrigger>
        </TabsList>

        <TabsContent value="projects" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Your Projects</h2>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              New Project
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {projects.map((project) => (
              <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="aspect-video bg-gray-100 rounded-md mb-2 flex items-center justify-center">
                    {project.posterUrl ? (
                      <img
                        src={project.posterUrl || "/placeholder.svg"}
                        alt={project.name}
                        className="w-full h-full object-cover rounded-md"
                      />
                    ) : (
                      <Play className="h-8 w-8 text-gray-400" />
                    )}
                  </div>
                  <CardTitle className="text-lg">{project.name}</CardTitle>
                  <CardDescription>{project.org.name}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{project._count.cuts} cuts</span>
                    <span>{project._count.assets} assets</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Updated {new Date(project.updatedAt).toLocaleDateString()}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="jobs" className="space-y-4">
          <h2 className="text-2xl font-bold">Active Jobs</h2>
          <div className="space-y-3">
            {activeJobs.map((job) => (
              <Card key={job.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <p className="font-medium">{job.type}</p>
                      <p className="text-sm text-muted-foreground">{job.project}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{job.progress}%</p>
                      <Clock className="h-4 w-4 text-muted-foreground inline" />
                    </div>
                  </div>
                  <Progress value={job.progress} className="w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <h2 className="text-2xl font-bold">Live Sessions</h2>
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No active screening sessions</p>
            <Button variant="outline" className="mt-4 bg-transparent">
              Start New Session
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
