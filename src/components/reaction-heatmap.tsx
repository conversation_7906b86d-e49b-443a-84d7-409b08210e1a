"use client"

import { useMemo } from "react"

interface ReactionData {
  atMs: number
  kind: string
  count: number
}

interface ReactionHeatmapProps {
  reactions: ReactionData[]
  duration: number
  className?: string
}

export function ReactionHeatmap({ reactions, duration, className = "" }: ReactionHeatmapProps) {
  const heatmapData = useMemo(() => {
    const buckets = Math.floor(duration / 1000) // 1 second buckets
    const data = new Array(buckets).fill(0)

    reactions.forEach((reaction) => {
      const bucket = Math.floor(reaction.atMs / 1000)
      if (bucket < buckets) {
        data[bucket] += reaction.count
      }
    })

    const maxValue = Math.max(...data, 1)
    return data.map((value) => value / maxValue)
  }, [reactions, duration])

  return (
    <div className={`flex h-8 bg-gray-100 rounded overflow-hidden ${className}`}>
      {heatmapData.map((intensity, index) => (
        <div
          key={index}
          className="flex-1 transition-all hover:opacity-80"
          style={{
            backgroundColor: intensity > 0 ? `rgba(99, 102, 241, ${0.2 + intensity * 0.8})` : "transparent",
          }}
          title={`${index}s: ${Math.round(intensity * 100)}% engagement`}
        />
      ))}
    </div>
  )
}
