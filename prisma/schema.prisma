generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  role          String    @default("USER")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts    Account[]
  sessions    Session[]
  memberships OrgMember[]
  reactions   Reaction[]
  comments    Comment[]
  wallet      Wallet?
  rewards     Reward[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Org {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  members  OrgMember[]
  projects Project[]

  @@map("orgs")
}

model OrgMember {
  id     String @id @default(cuid())
  orgId  String
  userId String
  role   String @default("MEMBER")

  org  Org  @relation(fields: [orgId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([orgId, userId])
  @@map("org_members")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  posterUrl   String?
  orgId       String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  org                Org                @relation(fields: [orgId], references: [id], onDelete: Cascade)
  assets             Asset[]
  cuts               Cut[]
  complianceReports  ComplianceReport[]
  renderJobs         RenderJob[]

  @@map("projects")
}

model Asset {
  id          String   @id @default(cuid())
  filename    String
  originalUrl String
  proxyUrl    String?
  mimeType    String
  size        Int
  duration    Float?
  width       Int?
  height      Int?
  projectId   String
  createdAt   DateTime @default(now())

  project   Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  cuts      Cut[]
  variants  Variant[]

  @@map("assets")
}

model Cut {
  id          String   @id @default(cuid())
  name        String
  description String?
  sourceAssetId String
  proxyAssetId  String?
  projectId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  project           Project            @relation(fields: [projectId], references: [id], onDelete: Cascade)
  sourceAsset       Asset              @relation(fields: [sourceAssetId], references: [id])
  screeningSessions ScreeningSession[]
  suggestions       Suggestion[]
  changeLists       ChangeList[]
  variants          Variant[]
  complianceReports ComplianceReport[]

  @@map("cuts")
}

model ScreeningSession {
  id        String   @id @default(cuid())
  name      String
  joinCode  String   @unique
  cutId     String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  cut       Cut        @relation(fields: [cutId], references: [id], onDelete: Cascade)
  reactions Reaction[]
  comments  Comment[]
  rewards   Reward[]

  @@map("screening_sessions")
}

model Reaction {
  id        String   @id @default(cuid())
  sessionId String
  userId    String?
  kind      String   // love, like, laugh, gasp, confused, sad
  atMs      Int
  createdAt DateTime @default(now())

  session ScreeningSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User?            @relation(fields: [userId], references: [id])

  @@map("reactions")
}

model Comment {
  id        String   @id @default(cuid())
  sessionId String
  userId    String?
  text      String
  atMs      Int
  createdAt DateTime @default(now())

  session ScreeningSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  user    User?            @relation(fields: [userId], references: [id])

  @@map("comments")
}

model Suggestion {
  id          String   @id @default(cuid())
  cutId       String
  type        String   // narrative, compliance
  title       String
  description String
  edlPatch    Json
  status      String   @default("PENDING") // PENDING, ACCEPTED, REJECTED
  createdAt   DateTime @default(now())

  cut Cut @relation(fields: [cutId], references: [id], onDelete: Cascade)

  @@map("suggestions")
}

model ChangeList {
  id          String   @id @default(cuid())
  cutId       String
  name        String
  description String?
  edl         Json
  createdAt   DateTime @default(now())

  cut      Cut       @relation(fields: [cutId], references: [id], onDelete: Cascade)
  variants Variant[]

  @@map("change_lists")
}

model Variant {
  id           String   @id @default(cuid())
  cutId        String
  changeListId String
  name         String
  outputAssetId String?
  status       String   @default("PENDING") // PENDING, RENDERING, COMPLETED, FAILED
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  cut        Cut        @relation(fields: [cutId], references: [id], onDelete: Cascade)
  changeList ChangeList @relation(fields: [changeListId], references: [id])
  outputAsset Asset?    @relation(fields: [outputAssetId], references: [id])

  @@map("variants")
}

model ComplianceReport {
  id        String   @id @default(cuid())
  cutId     String
  projectId String
  score     Int      // 0-100
  issues    Json
  reportUrl String?
  createdAt DateTime @default(now())

  cut     Cut     @relation(fields: [cutId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("compliance_reports")
}

model RenderJob {
  id        String   @id @default(cuid())
  projectId String
  type      String   // proxy, variant, compliance
  status    String   @default("PENDING") // PENDING, PROCESSING, COMPLETED, FAILED
  progress  Int      @default(0)
  metadata  Json?
  error     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("render_jobs")
}

model FeatureFlag {
  id      String  @id @default(cuid())
  name    String  @unique
  enabled Boolean @default(false)

  @@map("feature_flags")
}

// Optional Web3 models (behind FEATURE_WEB3 flag)
model Wallet {
  id      String @id @default(cuid())
  userId  String @unique
  address String @unique

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("wallets")
}

model Reward {
  id        String   @id @default(cuid())
  userId    String
  sessionId String
  amount    String   // Wei amount as string
  txHash    String?
  createdAt DateTime @default(now())

  user    User             @relation(fields: [userId], references: [id])
  session ScreeningSession @relation(fields: [sessionId], references: [id])

  @@map("rewards")
}
