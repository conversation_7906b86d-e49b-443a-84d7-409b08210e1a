import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

async function main() {
  console.log("Seeding database...")

  // Create demo organization
  const org = await prisma.org.create({
    data: {
      name: "Demo Studio",
      slug: "demo-studio",
    },
  })

  // Create demo user
  const user = await prisma.user.create({
    data: {
      name: "Demo User",
      email: "<EMAIL>",
      role: "ADMIN",
    },
  })

  // Add user to org
  await prisma.orgMember.create({
    data: {
      orgId: org.id,
      userId: user.id,
      role: "ADMIN",
    },
  })

  // Create demo project
  const project = await prisma.project.create({
    data: {
      name: "Three-Minute Short",
      description: "A demo short film for testing CrowdScreen Studio features",
      orgId: org.id,
      posterUrl: "/abstract-movie-poster.png",
    },
  })

  // Create demo asset
  const asset = await prisma.asset.create({
    data: {
      filename: "demo-short.mp4",
      originalUrl: "uploads/demo/demo-short.mp4",
      proxyUrl: "hls/demo/demo-short/index.m3u8",
      mimeType: "video/mp4",
      size: 50000000, // 50MB
      duration: 180, // 3 minutes
      width: 1920,
      height: 1080,
      projectId: project.id,
    },
  })

  // Create demo cut
  const cut = await prisma.cut.create({
    data: {
      name: "Main Cut",
      description: "Primary version of the three-minute short",
      sourceAssetId: asset.id,
      proxyAssetId: asset.id,
      projectId: project.id,
    },
  })

  // Create demo screening session
  const session = await prisma.screeningSession.create({
    data: {
      name: "Test Screening",
      joinCode: "DEMO01",
      cutId: cut.id,
      isActive: true,
    },
  })

  // Seed reactions at specific timestamps
  const reactionTypes = ["love", "like", "laugh", "gasp", "confused", "sad"]
  const timestamps = [15000, 45000, 90000] // 0:15, 0:45, 1:30

  for (const timestamp of timestamps) {
    for (let i = 0; i < 10; i++) {
      await prisma.reaction.create({
        data: {
          sessionId: session.id,
          kind: reactionTypes[Math.floor(Math.random() * reactionTypes.length)],
          atMs: timestamp + Math.random() * 5000 - 2500, // ±2.5s variance
          userId: Math.random() > 0.5 ? user.id : null, // Some anonymous reactions
        },
      })
    }
  }

  // Seed comments
  await prisma.comment.create({
    data: {
      sessionId: session.id,
      userId: user.id,
      text: "Great opening sequence!",
      atMs: 15000,
    },
  })

  await prisma.comment.create({
    data: {
      sessionId: session.id,
      text: "The pacing feels a bit slow here",
      atMs: 45000,
    },
  })

  // Create compliance report with flashing issue
  await prisma.complianceReport.create({
    data: {
      cutId: cut.id,
      projectId: project.id,
      score: 85,
      issues: [
        {
          type: "flashing",
          severity: "medium",
          startMs: 42000,
          endMs: 44000,
          description: "Rapid luminance changes detected during action sequence",
          recommendation: "Apply luminance limiter or add content warning",
        },
      ],
      reportUrl: `reports/${project.id}/${cut.id}/compliance.pdf`,
    },
  })

  // Create narrative suggestion
  await prisma.suggestion.create({
    data: {
      cutId: cut.id,
      type: "narrative",
      title: "Tighten Intro -6s",
      description: "Remove slow section at beginning to improve engagement",
      edlPatch: {
        operation: "trim",
        startMs: 0,
        endMs: 6000,
        action: "remove",
      },
      status: "PENDING",
    },
  })

  // Initialize feature flags
  await prisma.featureFlag.createMany({
    data: [
      { name: "FEATURE_WEB3", enabled: false },
      { name: "FEATURE_ADVANCED_ANALYTICS", enabled: true },
      { name: "FEATURE_AI_SUGGESTIONS", enabled: true },
    ],
  })

  console.log("Database seeded successfully!")
  console.log(`Demo project: ${project.name}`)
  console.log(`Join code: ${session.joinCode}`)
  console.log(`Demo user: ${user.email}`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
