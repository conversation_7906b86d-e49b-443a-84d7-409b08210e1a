version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: crowdscreen
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data

  web:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=********************************************/crowdscreen
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_BUCKET=crowdscreen
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret-key
      - FEATURE_WEB3=false
      - FEATURE_ADVANCED_ANALYTICS=true
    depends_on:
      - postgres
      - redis
      - minio
    volumes:
      - .:/app
      - /app/node_modules

  worker:
    build: 
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - DATABASE_URL=********************************************/crowdscreen
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_BUCKET=crowdscreen
      - FFMPEG_PATH=/usr/bin/ffmpeg
      - FFPROBE_PATH=/usr/bin/ffprobe
    depends_on:
      - postgres
      - redis
      - minio
    volumes:
      - .:/app
      - /app/node_modules

volumes:
  postgres_data:
  redis_data:
  minio_data:
