# CrowdScreen Studio

A production-ready SaaS platform for remote test screenings of short films and cuts, with real-time audience reactions, compliance checking, and AI-powered editing suggestions.

## Features

- 🎬 **Remote Screenings**: Host test screenings with shareable join codes
- 📊 **Real-time Reactions**: Collect timestamped audience reactions and comments
- 🛡️ **Compliance Checking**: Automated scanning for flashing/strobe risks, profanity, and loudness issues
- ✂️ **Smart Editing**: AI-generated suggestions for narrative improvements
- 🎞️ **Variant Generation**: Create A/B test versions with EDL-based editing
- 📈 **Analytics**: Sentiment analysis and engagement metrics
- 🔄 **Background Processing**: Scalable video transcoding and analysis

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Next.js API Routes, Prisma, PostgreSQL
- **Storage**: MinIO (S3-compatible) for assets and HLS streaming
- **Jobs**: Redis + BullMQ with dedicated worker containers
- **Real-time**: Socket.IO for live reactions and progress updates
- **Video**: FFmpeg for transcoding and analysis

## Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- pnpm (recommended)

### Demo Setup

\`\`\`bash
# Clone and install
git clone <repo-url>
cd crowdscreen-studio
pnpm install

# Start demo (resets DB and starts all services)
make demo
\`\`\`

The demo will be available at http://localhost:3000 with:
- **Demo login**: <EMAIL>
- **Join code**: DEMO01
- **Pre-seeded project**: "Three-Minute Short"

### Development Setup

\`\`\`bash
# Install dependencies
pnpm install

# Copy environment variables
cp .env.example .env

# Start infrastructure
docker-compose up -d postgres redis minio

# Setup database
pnpm db:push
pnpm db:seed

# Start development server
pnpm dev

# Start worker (in separate terminal)
pnpm worker
\`\`\`

## Demo Flow

1. **Open Project**: Navigate to "Three-Minute Short" project
2. **Compliance Scan**: Run scan to see pre-existing flashing issue at ~0:42
3. **Start Session**: Create new screening session
4. **Join as Viewer**: Open guest link in separate tab/device
5. **React Live**: Send reactions around 0:45 to create engagement trough
6. **View Suggestions**: Check narrative suggestions tab
7. **Accept Edit**: Accept "Tighten Intro -6s" suggestion
8. **Render Variant**: Create and render new version
9. **Compare Results**: View improved engagement in variant
10. **Export**: Download compliance PDF and EDL JSON

## API Documentation

- **OpenAPI Spec**: `/openapi.yml`
- **Postman Collection**: `/postman-collection.json`
- **Interactive Docs**: http://localhost:3000/api/docs (when running)

### Key Endpoints

\`\`\`
GET    /api/me                              # Current user
GET    /api/projects                        # List projects
POST   /api/sessions                        # Create screening
POST   /api/sessions/{joinCode}/join        # Join session
POST   /api/cuts/{id}/compliance/scan       # Start compliance scan
POST   /api/cuts/{id}/suggestions/narrative # Generate suggestions
POST   /api/cuts/{id}/variants              # Create variant
\`\`\`

## Architecture

### Core Entities

- **Users & Orgs**: Multi-tenant organization structure
- **Projects**: Container for all content and screenings
- **Assets**: Raw uploads and transcoded proxies
- **Cuts**: Specific versions/edits of content
- **Sessions**: Live screening rooms with join codes
- **Reactions/Comments**: Timestamped audience feedback
- **Suggestions**: AI-generated editing recommendations
- **Variants**: Alternative cuts based on change lists

### Storage Layout

\`\`\`
MinIO Bucket Structure:
├── uploads/{orgId}/{projectId}/     # Raw uploads
├── hls/{projectId}/{cutId}/         # HLS streaming files
├── reports/{projectId}/{cutId}/     # Compliance reports
└── edl/{projectId}/{variantId}/     # Change lists & EDL exports
\`\`\`

### Background Jobs

1. **Proxy Transcode**: Convert uploads to HLS for streaming
2. **Variant Render**: Apply EDL changes to create new versions
3. **Compliance Scan**: Analyze content for issues and generate reports

## Compliance Features

### Flashing/Strobe Detection
- Analyzes frame luminance changes
- Flags rapid alternating patterns
- Configurable sensitivity thresholds

### Caption Profanity Checking
- Parses VTT/SRT subtitle files
- Cross-references with speech energy
- Reduces false positives

### Loudness Analysis
- LUFS measurement and normalization
- Web content standards (-14 LUFS ±2)
- Peak and sustained violation detection

## Sentiment Analysis

Real-time sentiment scoring based on reaction weights:
- **Positive**: love×2 + like + laugh×1.5 + gasp×1.2
- **Negative**: confused + sad×1.3
- **Smoothing**: 5-second moving average
- **Trough Detection**: 25% below median for 5+ seconds

## EDL Format

Compact JSON structure for editing operations:

\`\`\`json
{
  "clips": [
    {
      "src": "source-file.mp4",
      "inMs": 0,
      "outMs": 30000,
      "audioGain": 1.0
    }
  ],
  "metadata": {
    "title": "Tightened Intro",
    "version": "1.0"
  }
}
\`\`\`

## Testing

\`\`\`bash
# Unit tests
pnpm test

# E2E tests
pnpm test:e2e

# API testing with Postman
newman run postman-collection.json
\`\`\`

## Deployment

### Docker Production

\`\`\`bash
# Build and deploy
docker-compose -f docker-compose.prod.yml up -d

# Scale workers
docker-compose -f docker-compose.prod.yml up -d --scale worker=3
\`\`\`

### Environment Variables

See `.env.example` for all required configuration options.

## Feature Flags

- `FEATURE_WEB3`: Enable blockchain audit trails and rewards
- `FEATURE_ADVANCED_ANALYTICS`: Enhanced metrics and reporting
- `FEATURE_AI_SUGGESTIONS`: ML-powered editing recommendations

## Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## License

MIT License - see LICENSE file for details.

## Support

- 📧 Email: <EMAIL>
- 📖 Documentation: https://docs.crowdscreen.studio
- 🐛 Issues: GitHub Issues
- 💬 Discord: https://discord.gg/crowdscreen
