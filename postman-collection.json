{"info": {"name": "CrowdScreen Studio API", "description": "Complete API collection for CrowdScreen Studio", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000/api"}, {"key": "authToken", "value": ""}, {"key": "projectId", "value": ""}, {"key": "cutId", "value": ""}, {"key": "sessionId", "value": ""}], "item": [{"name": "Authentication", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/me", "host": ["{{baseUrl}}"], "path": ["me"]}}}]}, {"name": "Projects", "item": [{"name": "List Projects", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const projects = pm.response.json();", "    if (projects.length > 0) {", "        pm.collectionVariables.set('projectId', projects[0].id);", "    }", "}"]}}]}, {"name": "Get Project Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/projects/{{projectId}}", "host": ["{{baseUrl}}"], "path": ["projects", "{{projectId}}"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const project = pm.response.json();", "    if (project.cuts && project.cuts.length > 0) {", "        pm.collectionVariables.set('cutId', project.cuts[0].id);", "    }", "}"]}}]}]}, {"name": "Screening Sessions", "item": [{"name": "Create Session", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Screening Session\",\n  \"cutId\": \"{{cutId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/sessions", "host": ["{{baseUrl}}"], "path": ["sessions"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const session = pm.response.json();", "    pm.collectionVariables.set('sessionId', session.id);", "    pm.collectionVariables.set('joinCode', session.joinCode);", "}"]}}]}, {"name": "Join Session", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/sessions/{{joinCode}}/join", "host": ["{{baseUrl}}"], "path": ["sessions", "{{joinCode}}", "join"]}}}]}, {"name": "Compliance", "item": [{"name": "Start Compliance Scan", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/cuts/{{cutId}}/compliance/scan", "host": ["{{baseUrl}}"], "path": ["cuts", "{{cutId}}", "compliance", "scan"]}}}]}, {"name": "Suggestions", "item": [{"name": "Generate Narrative Suggestions", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/cuts/{{cutId}}/suggestions/narrative", "host": ["{{baseUrl}}"], "path": ["cuts", "{{cutId}}", "suggestions", "narrative"]}}}]}]}