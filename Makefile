.PHONY: dev build start test seed demo clean

# Development
dev:
	pnpm dev

# Build
build:
	pnpm build

# Start production
start:
	pnpm start

# Database operations
db-generate:
	pnpm db:generate

db-push:
	pnpm db:push

db-migrate:
	pnpm db:migrate

seed:
	pnpm db:seed

# Worker
worker:
	pnpm worker

# Testing
test:
	pnpm test

test-e2e:
	pnpm test:e2e

# Demo setup
demo:
	@echo "🚀 Starting CrowdScreen Studio Demo..."
	docker-compose down
	docker-compose up -d postgres redis minio
	@echo "⏳ Waiting for services to start..."
	sleep 10
	pnpm db:push
	pnpm db:seed
	@echo "🎬 Starting all services..."
	docker-compose up
	@echo "✅ Demo ready at http://localhost:3000"
	@echo "📧 Demo login: <EMAIL>"
	@echo "🎫 Join code: DEMO01"

# Cleanup
clean:
	docker-compose down -v
	docker system prune -f

# Health check
health:
	@echo "🔍 Checking service health..."
	@curl -f http://localhost:3000/api/health || echo "❌ Web service down"
	@docker-compose ps
