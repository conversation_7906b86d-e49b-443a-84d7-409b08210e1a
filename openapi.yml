openapi: 3.1.0
info:
  title: CrowdScreen Studio API
  description: Remote test screenings and compliance checking for content creators
  version: 1.0.0
  contact:
    name: CrowdScreen Studio
    url: https://crowdscreen.studio
servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://api.crowdscreen.studio
    description: Production server

paths:
  /me:
    get:
      summary: Get current user profile
      tags: [Auth]
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /projects:
    get:
      summary: List user's projects
      tags: [Projects]
      security:
        - bearerAuth: []
      responses:
        '200':
          description: List of projects
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Project'
    post:
      summary: Create new project
      tags: [Projects]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name, orgId]
              properties:
                name:
                  type: string
                  minLength: 1
                description:
                  type: string
                orgId:
                  type: string
      responses:
        '201':
          description: Project created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Project'

  /projects/{id}:
    get:
      summary: Get project details
      tags: [Projects]
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Project details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetail'

  /sessions:
    post:
      summary: Create screening session
      tags: [Screenings]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [name, cutId]
              properties:
                name:
                  type: string
                cutId:
                  type: string
      responses:
        '201':
          description: Session created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreeningSession'

  /sessions/{joinCode}/join:
    post:
      summary: Join screening session
      tags: [Screenings]
      parameters:
        - name: joinCode
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Session joined
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScreeningSession'

  /cuts/{cutId}/compliance/scan:
    post:
      summary: Start compliance scan
      tags: [Compliance]
      security:
        - bearerAuth: []
      parameters:
        - name: cutId
          in: path
          required: true
          schema:
            type: string
      responses:
        '202':
          description: Scan started
          content:
            application/json:
              schema:
                type: object
                properties:
                  jobId:
                    type: string
                  status:
                    type: string
                    enum: [PENDING, PROCESSING]

  /cuts/{cutId}/suggestions/narrative:
    post:
      summary: Generate narrative suggestions
      tags: [Suggestions]
      security:
        - bearerAuth: []
      parameters:
        - name: cutId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Suggestions generated
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Suggestion'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    User:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        email:
          type: string
          format: email
        role:
          type: string
          enum: [USER, ADMIN]
        createdAt:
          type: string
          format: date-time

    Project:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        posterUrl:
          type: string
        orgId:
          type: string
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time

    ProjectDetail:
      allOf:
        - $ref: '#/components/schemas/Project'
        - type: object
          properties:
            cuts:
              type: array
              items:
                $ref: '#/components/schemas/Cut'
            assets:
              type: array
              items:
                $ref: '#/components/schemas/Asset'

    Cut:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        sourceAssetId:
          type: string
        proxyAssetId:
          type: string
        projectId:
          type: string
        createdAt:
          type: string
          format: date-time

    Asset:
      type: object
      properties:
        id:
          type: string
        filename:
          type: string
        originalUrl:
          type: string
        proxyUrl:
          type: string
        mimeType:
          type: string
        size:
          type: integer
        duration:
          type: number
        width:
          type: integer
        height:
          type: integer

    ScreeningSession:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        joinCode:
          type: string
        cutId:
          type: string
        isActive:
          type: boolean
        createdAt:
          type: string
          format: date-time

    Suggestion:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
          enum: [narrative, compliance]
        title:
          type: string
        description:
          type: string
        edlPatch:
          type: object
        status:
          type: string
          enum: [PENDING, ACCEPTED, REJECTED]

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: Unauthorized
