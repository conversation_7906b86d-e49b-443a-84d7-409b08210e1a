import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { z } from "zod"

const createSessionSchema = z.object({
  name: z.string().min(1),
  cutId: z.string(),
})

function generateJoinCode(): string {
  return Math.random().toString(36).substring(2, 8).toUpperCase()
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { name, cutId } = createSessionSchema.parse(body)

    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: cutId,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    let joinCode = generateJoinCode()
    let attempts = 0

    // Ensure unique join code
    while (attempts < 10) {
      const existing = await db.screeningSession.findUnique({
        where: { joinCode },
      })

      if (!existing) break

      joinCode = generateJoinCode()
      attempts++
    }

    const screeningSession = await db.screeningSession.create({
      data: {
        name,
        cutId,
        joinCode,
      },
      include: {
        cut: {
          include: {
            sourceAsset: true,
            project: true,
          },
        },
      },
    })

    return NextResponse.json(screeningSession)
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 })
  }
}
