import { type NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await db.screeningSession.findUnique({
    where: {
      joinCode: params.id,
    },
    include: {
      cut: {
        include: {
          sourceAsset: true,
          project: true,
        },
      },
    },
  })

  if (!session || !session.isActive) {
    return NextResponse.json({ error: "Session not found or inactive" }, { status: 404 })
  }

  return NextResponse.json(session)
}
