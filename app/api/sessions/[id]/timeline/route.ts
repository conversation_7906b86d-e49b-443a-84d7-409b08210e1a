import { type NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Find session by ID or join code
    const session = await db.screeningSession.findFirst({
      where: {
        OR: [
          { id: params.id },
          { joinCode: params.id },
        ],
      },
      include: {
        cut: {
          include: {
            sourceAsset: true,
          },
        },
        reactions: {
          orderBy: { atMs: "asc" },
        },
        comments: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: { atMs: "asc" },
        },
      },
    })

    if (!session) {
      return NextResponse.json({ error: "Session not found" }, { status: 404 })
    }

    // Group reactions into time buckets (5-second intervals)
    const bucketSize = 5000 // 5 seconds in milliseconds
    const duration = session.cut.sourceAsset?.duration || 180 // Default 3 minutes
    const totalBuckets = Math.ceil((duration * 1000) / bucketSize)

    const buckets = Array.from({ length: totalBuckets }, (_, i) => ({
      startMs: i * bucketSize,
      endMs: (i + 1) * bucketSize,
      reactions: {
        love: 0,
        like: 0,
        laugh: 0,
        gasp: 0,
        confused: 0,
        sad: 0,
      },
      sentiment: 0,
      engagement: 0,
    }))

    // Distribute reactions into buckets
    session.reactions.forEach((reaction) => {
      const bucketIndex = Math.floor(reaction.atMs / bucketSize)
      if (bucketIndex >= 0 && bucketIndex < buckets.length) {
        const bucket = buckets[bucketIndex]
        if (bucket.reactions[reaction.kind as keyof typeof bucket.reactions] !== undefined) {
          bucket.reactions[reaction.kind as keyof typeof bucket.reactions]++
        }
      }
    })

    // Calculate sentiment and engagement for each bucket
    buckets.forEach((bucket) => {
      const { love, like, laugh, gasp, confused, sad } = bucket.reactions
      
      // Sentiment calculation (positive - negative)
      const positive = love * 2 + like + laugh * 1.5 + gasp * 1.2
      const negative = confused + sad * 1.3
      bucket.sentiment = positive - negative

      // Engagement calculation (total reactions)
      bucket.engagement = love + like + laugh + gasp + confused + sad
    })

    // Calculate overall statistics
    const totalReactions = session.reactions.length
    const avgSentiment = buckets.reduce((sum, b) => sum + b.sentiment, 0) / buckets.length
    const avgEngagement = buckets.reduce((sum, b) => sum + b.engagement, 0) / buckets.length

    // Find engagement troughs (25% below median for 5+ seconds)
    const medianEngagement = buckets
      .map(b => b.engagement)
      .sort((a, b) => a - b)[Math.floor(buckets.length / 2)]
    
    const threshold = medianEngagement * 0.75
    const troughs = []
    let currentTrough = null

    for (let i = 0; i < buckets.length; i++) {
      const bucket = buckets[i]
      
      if (bucket.engagement < threshold) {
        if (!currentTrough) {
          currentTrough = {
            startMs: bucket.startMs,
            endMs: bucket.endMs,
            avgEngagement: bucket.engagement,
            bucketCount: 1,
          }
        } else {
          currentTrough.endMs = bucket.endMs
          currentTrough.avgEngagement = 
            (currentTrough.avgEngagement * currentTrough.bucketCount + bucket.engagement) / 
            (currentTrough.bucketCount + 1)
          currentTrough.bucketCount++
        }
      } else {
        if (currentTrough && currentTrough.bucketCount >= 1) { // 5+ seconds
          troughs.push(currentTrough)
        }
        currentTrough = null
      }
    }

    // Add final trough if exists
    if (currentTrough && currentTrough.bucketCount >= 1) {
      troughs.push(currentTrough)
    }

    return NextResponse.json({
      sessionId: session.id,
      joinCode: session.joinCode,
      duration: duration * 1000, // Convert to milliseconds
      buckets,
      statistics: {
        totalReactions,
        avgSentiment,
        avgEngagement,
        medianEngagement,
      },
      troughs,
      comments: session.comments,
    })
  } catch (error) {
    console.error("Error fetching session timeline:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
