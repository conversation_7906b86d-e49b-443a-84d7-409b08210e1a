import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { variantQueue } from "@/lib/queue"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the variant
    const variant = await db.variant.findFirst({
      where: {
        id: params.id,
        cut: {
          project: {
            org: {
              members: {
                some: {
                  userId: session.user.id,
                },
              },
            },
          },
        },
      },
      include: {
        cut: {
          include: {
            project: true,
          },
        },
        changeList: true,
      },
    })

    if (!variant) {
      return NextResponse.json({ error: "Variant not found" }, { status: 404 })
    }

    // Check if variant is already rendering or completed
    if (variant.status === "RENDERING") {
      return NextResponse.json({ error: "Variant is already rendering" }, { status: 409 })
    }

    if (variant.status === "COMPLETED") {
      return NextResponse.json({ error: "Variant is already completed" }, { status: 409 })
    }

    // Update variant status
    await db.variant.update({
      where: { id: variant.id },
      data: { status: "RENDERING" },
    })

    // Create render job
    const renderJob = await db.renderJob.create({
      data: {
        projectId: variant.cut.projectId,
        type: "variant",
        status: "PROCESSING",
        metadata: {
          variantId: variant.id,
          cutId: variant.cutId,
          changeListId: variant.changeListId,
        },
      },
    })

    // Queue variant render job
    await variantQueue.add("variant-render", {
      variantId: variant.id,
      cutId: variant.cutId,
      changeListId: variant.changeListId,
    })

    return NextResponse.json(
      {
        variantId: variant.id,
        renderJobId: renderJob.id,
        status: "RENDERING",
        message: "Variant render started",
      },
      { status: 202 }
    )
  } catch (error) {
    console.error("Error starting variant render:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
