import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { getUploadUrl } from "@/lib/minio"
import { z } from "zod"
import { v4 as uuidv4 } from "uuid"

const presignSchema = z.object({
  filename: z.string(),
  contentType: z.string(),
  orgId: z.string(),
  projectId: z.string(),
})

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { filename, contentType, orgId, projectId } = presignSchema.parse(body)

    const fileId = uuidv4()
    const key = `uploads/${orgId}/${projectId}/${fileId}-${filename}`

    const uploadUrl = await getUploadUrl(key, 3600)

    return NextResponse.json({
      uploadUrl,
      key,
      fileId,
    })
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 })
  }
}
