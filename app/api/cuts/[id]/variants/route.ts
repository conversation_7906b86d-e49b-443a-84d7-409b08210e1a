import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { variantQueue } from "@/lib/queue"
import { z } from "zod"

const createVariantSchema = z.object({
  name: z.string().min(1),
  edl: z.object({
    clips: z.array(z.object({
      src: z.string(),
      inMs: z.number(),
      outMs: z.number(),
      audioGain: z.number().optional().default(1.0),
    })),
    metadata: z.object({
      title: z.string(),
      version: z.string(),
    }),
  }),
})

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { name, edl } = createVariantSchema.parse(body)

    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
      include: {
        project: true,
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Create change list
    const changeList = await db.changeList.create({
      data: {
        cutId: cut.id,
        name: `${name} - Change List`,
        description: `EDL for variant: ${name}`,
        edl,
      },
    })

    // Create variant
    const variant = await db.variant.create({
      data: {
        cutId: cut.id,
        changeListId: changeList.id,
        name,
        status: "PENDING",
      },
      include: {
        changeList: true,
      },
    })

    // Create render job
    const renderJob = await db.renderJob.create({
      data: {
        projectId: cut.projectId,
        type: "variant",
        status: "PENDING",
        metadata: {
          variantId: variant.id,
          cutId: cut.id,
          changeListId: changeList.id,
        },
      },
    })

    // Queue variant render job
    await variantQueue.add("variant-render", {
      variantId: variant.id,
      cutId: cut.id,
      changeListId: changeList.id,
    })

    return NextResponse.json(
      {
        ...variant,
        renderJobId: renderJob.id,
      },
      { status: 201 }
    )
  } catch (error) {
    console.error("Error creating variant:", error)
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid request data", details: error.errors }, { status: 400 })
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Get variants for this cut
    const variants = await db.variant.findMany({
      where: {
        cutId: params.id,
      },
      include: {
        changeList: true,
        outputAsset: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(variants)
  } catch (error) {
    console.error("Error fetching variants:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
