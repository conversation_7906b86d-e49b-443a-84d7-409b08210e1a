import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
      include: {
        sourceAsset: true,
        screeningSessions: {
          include: {
            reactions: true,
            comments: true,
          },
        },
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Generate AI-powered narrative suggestions based on audience reactions
    const suggestions = await generateNarrativeSuggestions(cut)

    // Save suggestions to database
    const savedSuggestions = await Promise.all(
      suggestions.map((suggestion) =>
        db.suggestion.create({
          data: {
            cutId: cut.id,
            type: "narrative",
            title: suggestion.title,
            description: suggestion.description,
            edlPatch: suggestion.edlPatch,
            status: "PENDING",
          },
        })
      )
    )

    return NextResponse.json(savedSuggestions)
  } catch (error) {
    console.error("Error generating narrative suggestions:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Get existing suggestions
    const suggestions = await db.suggestion.findMany({
      where: {
        cutId: params.id,
        type: "narrative",
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    return NextResponse.json(suggestions)
  } catch (error) {
    console.error("Error fetching suggestions:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// Mock AI suggestion generator
async function generateNarrativeSuggestions(cut: {
  screeningSessions: Array<{
    reactions: Array<{ atMs: number; kind: string }>
  }>
}) {
  // Analyze reactions to find engagement troughs
  const allReactions = cut.screeningSessions.flatMap((session) => session.reactions)
  
  if (allReactions.length === 0) {
    return []
  }

  // Simple algorithm: find time periods with low engagement
  const suggestions = []

  // Example suggestion based on low engagement at beginning
  const earlyReactions = allReactions.filter((r) => r.atMs < 30000)
  if (earlyReactions.length < 5) {
    suggestions.push({
      title: "Tighten Opening",
      description: "Low engagement in first 30 seconds. Consider trimming or adding hook.",
      edlPatch: {
        operation: "trim",
        startMs: 0,
        endMs: 5000,
        action: "remove",
      },
    })
  }

  // Example suggestion for pacing
  suggestions.push({
    title: "Improve Pacing",
    description: "Add quick cuts during action sequence to maintain energy.",
    edlPatch: {
      operation: "edit",
      startMs: 60000,
      endMs: 90000,
      action: "quicken",
      factor: 1.2,
    },
  })

  return suggestions
}
