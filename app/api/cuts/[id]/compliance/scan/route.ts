import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { complianceQueue } from "@/lib/queue"

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
      include: {
        sourceAsset: true,
        project: true,
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Create render job
    const renderJob = await db.renderJob.create({
      data: {
        projectId: cut.projectId,
        type: "compliance",
        status: "PENDING",
        metadata: {
          cutId: cut.id,
          sourceAssetId: cut.sourceAssetId,
        },
      },
    })

    // Queue compliance scan job
    await complianceQueue.add("compliance-scan", {
      cutId: cut.id,
      sourceAssetId: cut.sourceAssetId,
    })

    return NextResponse.json(
      {
        jobId: renderJob.id,
        status: "PENDING",
        message: "Compliance scan started",
      },
      { status: 202 }
    )
  } catch (error) {
    console.error("Error starting compliance scan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
