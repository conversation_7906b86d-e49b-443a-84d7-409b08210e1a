import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    // Verify user has access to the cut
    const cut = await db.cut.findFirst({
      where: {
        id: params.id,
        project: {
          org: {
            members: {
              some: {
                userId: session.user.id,
              },
            },
          },
        },
      },
    })

    if (!cut) {
      return NextResponse.json({ error: "Cut not found" }, { status: 404 })
    }

    // Get latest compliance report
    const complianceReport = await db.complianceReport.findFirst({
      where: {
        cutId: params.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    })

    if (!complianceReport) {
      return NextResponse.json({ error: "No compliance report found" }, { status: 404 })
    }

    return NextResponse.json(complianceReport)
  } catch (error) {
    console.error("Error fetching compliance report:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
