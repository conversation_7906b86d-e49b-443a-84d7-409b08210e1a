import { NextResponse } from "next/server"
import { db } from "@/lib/db"
import { redis } from "@/lib/redis"
import { minioClient } from "@/lib/minio"
import { env } from "@/lib/env"

export async function GET() {
  const checks = {
    database: false,
    redis: false,
    minio: false,
    ffmpeg: false,
  }

  let overallStatus = "healthy"

  // Check database
  try {
    await db.$queryRaw`SELECT 1`
    checks.database = true
  } catch (error) {
    console.error("Database health check failed:", error)
    overallStatus = "unhealthy"
  }

  // Check Redis
  try {
    await redis.ping()
    checks.redis = true
  } catch (error) {
    console.error("Redis health check failed:", error)
    overallStatus = "unhealthy"
  }

  // Check MinIO
  try {
    await minioClient.bucketExists(env.MINIO_BUCKET)
    checks.minio = true
  } catch (error) {
    console.error("MinIO health check failed:", error)
    overallStatus = "unhealthy"
  }

  // Check FFmpeg (optional)
  if (env.FFMPEG_PATH) {
    try {
      const { spawn } = await import("child_process")
      const ffmpeg = spawn(env.FFMPEG_PATH, ["-version"])

      await new Promise((resolve, reject) => {
        ffmpeg.on("close", (code: number) => {
          if (code === 0) {
            checks.ffmpeg = true
            resolve(true)
          } else {
            reject(new Error(`FFmpeg exited with code ${code}`))
          }
        })
        ffmpeg.on("error", reject)
      })
    } catch (error) {
      console.error("FFmpeg health check failed:", error)
      // FFmpeg is optional, so don't mark overall status as unhealthy
    }
  }

  const status = overallStatus === "healthy" ? 200 : 503

  return NextResponse.json(
    {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks,
      version: "1.0.0",
    },
    { status }
  )
}
