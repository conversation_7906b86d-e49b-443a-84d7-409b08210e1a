import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { z } from "zod"

const createProjectSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  orgId: z.string(),
})

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const projects = await db.project.findMany({
    where: {
      org: {
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      org: true,
      _count: {
        select: {
          cuts: true,
          assets: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
  })

  return NextResponse.json(projects)
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  try {
    const body = await request.json()
    const { name, description, orgId } = createProjectSchema.parse(body)

    // Check if user is member of org
    const membership = await db.orgMember.findFirst({
      where: {
        orgId,
        userId: session.user.id,
      },
    })

    if (!membership) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    const project = await db.project.create({
      data: {
        name,
        description,
        orgId,
      },
      include: {
        org: true,
      },
    })

    return NextResponse.json(project)
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 })
  }
}
