import { type NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const project = await db.project.findFirst({
    where: {
      id: params.id,
      org: {
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      org: true,
      assets: {
        orderBy: { createdAt: "desc" },
      },
      cuts: {
        include: {
          sourceAsset: true,
          screeningSessions: {
            where: { isActive: true },
            take: 5,
          },
          _count: {
            select: {
              suggestions: true,
              variants: true,
            },
          },
        },
        orderBy: { updatedAt: "desc" },
      },
      complianceReports: {
        orderBy: { createdAt: "desc" },
        take: 5,
      },
      renderJobs: {
        where: {
          status: { in: ["PENDING", "PROCESSING"] },
        },
        orderBy: { createdAt: "desc" },
      },
    },
  })

  if (!project) {
    return NextResponse.json({ error: "Project not found" }, { status: 404 })
  }

  return NextResponse.json(project)
}
