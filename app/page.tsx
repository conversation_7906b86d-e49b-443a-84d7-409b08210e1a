import { getServerSession } from "next-auth"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { db } from "@/lib/db"
import { Dashboard } from "@/components/dashboard"

export default async function HomePage() {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/auth/signin")
  }

  const projects = await db.project.findMany({
    where: {
      org: {
        members: {
          some: {
            userId: session.user.id,
          },
        },
      },
    },
    include: {
      org: true,
      _count: {
        select: {
          cuts: true,
          assets: true,
        },
      },
    },
    orderBy: {
      updatedAt: "desc",
    },
  })

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">CrowdScreen Studio</h1>
        <p className="text-muted-foreground">Remote test screenings and compliance checking for your content</p>
      </div>

      <Dashboard projects={projects} />
    </div>
  )
}
