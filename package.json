{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "worker": "tsx src/workers/index.ts", "test": "vitest", "test:e2e": "playwright test", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth/core": "latest", "@auth/prisma-adapter": "latest", "@hookform/resolvers": "^3.9.1", "@prisma/client": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "bullmq": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "dotenv": "^17.2.1", "embla-carousel-react": "8.5.1", "fluent-ffmpeg": "latest", "geist": "^1.3.1", "hls.js": "latest", "input-otp": "1.4.1", "ioredis": "latest", "lucide-react": "^0.454.0", "minio": "latest", "next": "14.2.16", "next-auth": "latest", "next-themes": "^0.4.4", "nodemailer": "latest", "prisma": "latest", "react": "^18", "react-day-picker": "9.8.0", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.48.0", "@types/fluent-ffmpeg": "^2.1.26", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10", "eslint": "^8.57.1", "eslint-config-next": "15.4.6", "postcss": "^8.5", "tailwindcss": "^3.4.17", "tsx": "^4.19.0", "typescript": "^5", "vitest": "^2.1.0"}}